#!/usr/bin/env node

/**
 * Simple test script to verify the email queue system
 * Run with: node test-queue.js
 */

const fetch = require('node-fetch');
const { log } = require('@repo/observability/log');

const API_BASE = process.env.API_URL || 'http://localhost:3002';

async function testEmailQueue() {
  log.debug('🧪 Testing Email Queue System...\n');

  try {
    // Test 1: Check queue stats
    log.debug('1. Checking queue statistics...');
    const statsResponse = await fetch(`${API_BASE}/api/email-queue/stats`);
    const stats = await statsResponse.json();

    if (stats.success) {
      log.debug('✅ Queue stats retrieved successfully');
      log.debug(`   - Waiting: ${stats.data.waiting}`);
      log.debug(`   - Active: ${stats.data.active}`);
      log.debug(`   - Completed: ${stats.data.completed}`);
      log.debug(`   - Failed: ${stats.data.failed}`);
      log.debug(`   - Redis Healthy: ${stats.data.redisHealthy}`);
    } else {
      log.debug('❌ Failed to get queue stats:', stats.error);
    }

    log.debug('\n2. Adding test email to queue...');

    // Test 2: Add a test email to queue
    const testEmail = {
      type: 'return-request-auto-approved',
      to: '<EMAIL>',
      returnNumber: `TEST-${Date.now()}`,
      refundDays: '5-7',
      priority: 8,
    };

    const addResponse = await fetch(`${API_BASE}/api/email-queue/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testEmail),
    });

    const addResult = await addResponse.json();

    if (addResult.success) {
      log.debug('✅ Test email added to queue successfully');
      log.debug(`   - Job ID: ${addResult.jobId}`);
      log.debug(`   - Email Type: ${testEmail.type}`);
      log.debug(`   - Return Number: ${testEmail.returnNumber}`);
    } else {
      log.debug('❌ Failed to add email to queue:', addResult.error);
    }

    log.debug('\n3. Checking updated queue statistics...');

    // Test 3: Check stats again to see the change
    const updatedStatsResponse = await fetch(
      `${API_BASE}/api/email-queue/stats`
    );
    const updatedStats = await updatedStatsResponse.json();

    if (updatedStats.success) {
      log.debug('✅ Updated queue stats retrieved');
      log.debug(`   - Waiting: ${updatedStats.data.waiting}`);
      log.debug(`   - Active: ${updatedStats.data.active}`);
      log.debug(`   - Total: ${updatedStats.data.total}`);
    }

    log.debug('\n🎉 Email queue system test completed!');
    log.debug('\nNext steps:');
    log.debug('1. Start the email worker: cd apps/email-worker && pnpm dev');
    log.debug(
      '2. Monitor the admin interface: http://localhost:3001/email-queue'
    );
    log.debug('3. Check worker logs for email processing');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    log.debug('\nTroubleshooting:');
    log.debug(
      '1. Make sure the API server is running: cd apps/api && pnpm dev'
    );
    log.debug('2. Check Redis connection in environment variables');
    log.debug('3. Verify SendGrid configuration');
  }
}

// Run the test
testEmailQueue();
