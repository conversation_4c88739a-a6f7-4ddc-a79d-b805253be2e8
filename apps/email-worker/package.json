{"name": "email-worker", "version": "0.0.0", "private": true, "scripts": {"dev": "tsx watch src/index.ts", "start": "tsx src/index.ts", "build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit"}, "dependencies": {"@repo/email": "workspace:*", "@repo/email-queue": "workspace:*", "@repo/observability": "workspace:*", "dotenv": "^16.4.7"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "tsx": "^4.19.2", "typescript": "^5.8.3"}}