import 'dotenv/config';
import {
  checkRedisHealth,
  createEmailWorker,
  shutdownWorker,
} from '@repo/email-queue';
import { log } from '@repo/observability/log';

// Configuration
const WORKER_CONCURRENCY = Number.parseInt(
  process.env.EMAIL_WORKER_CONCURRENCY || '5'
);
const HEALTH_CHECK_INTERVAL = Number.parseInt(
  process.env.HEALTH_CHECK_INTERVAL || '30000'
); // 30 seconds

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
let worker: any = null;
let healthCheckInterval: NodeJS.Timeout | null = null;

// Health check function
const performHealthCheck = async () => {
  try {
    const isHealthy = await checkRedisHealth();
    if (isHealthy) {
      log.info('Email worker health check passed');
    } else {
      log.error('Email worker health check failed - Redis connection issue');
    }
  } catch (error) {
    log.error('Health check error', { error });
  }
};

// Start the worker
const startWorker = async () => {
  try {
    log.info('Starting email worker...', { concurrency: WORKER_CONCURRENCY });

    // Check Redis connection before starting
    const isRedisHealthy = await checkRedisHealth();
    if (!isRedisHealthy) {
      throw new Error('Redis connection failed - cannot start worker');
    }

    // Create and start the worker
    worker = createEmailWorker(WORKER_CONCURRENCY);

    // Set up health check interval
    healthCheckInterval = setInterval(
      performHealthCheck,
      HEALTH_CHECK_INTERVAL
    );

    log.info('Email worker started successfully', {
      concurrency: WORKER_CONCURRENCY,
      healthCheckInterval: HEALTH_CHECK_INTERVAL,
    });

    // Keep the process running
    process.on('SIGINT', gracefulShutdown);
    process.on('SIGTERM', gracefulShutdown);
    process.on('uncaughtException', (error) => {
      log.error('Uncaught exception in email worker', { error });
      gracefulShutdown();
    });
    process.on('unhandledRejection', (reason, promise) => {
      log.error('Unhandled rejection in email worker', { reason, promise });
      gracefulShutdown();
    });
  } catch (error) {
    log.error('Failed to start email worker', { error });
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = async () => {
  log.info('Initiating graceful shutdown of email worker...');

  try {
    // Clear health check interval
    if (healthCheckInterval) {
      clearInterval(healthCheckInterval);
      healthCheckInterval = null;
    }

    // Shutdown worker
    if (worker) {
      await shutdownWorker(worker);
      worker = null;
    }

    log.info('Email worker shut down successfully');
    process.exit(0);
  } catch (error) {
    log.error('Error during graceful shutdown', { error });
    process.exit(1);
  }
};

// Start the worker
startWorker().catch((error) => {
  log.error('Failed to start email worker', { error });
  process.exit(1);
});
