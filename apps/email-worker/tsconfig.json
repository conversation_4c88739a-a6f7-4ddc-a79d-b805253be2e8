{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"lib": ["es2022"], "module": "ESNext", "target": "es2022", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "outDir": "./dist", "rootDir": "./src", "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}