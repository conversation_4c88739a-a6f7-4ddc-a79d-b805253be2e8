import { NextRequest, NextResponse } from 'next/server';
import { addEmailToQueue, emailJobDataSchema } from '@repo/email-queue';
import { log } from '@repo/observability/log';
import { z } from 'zod';

const requestSchema = emailJobDataSchema.extend({
  options: z.object({
    priority: z.number().min(1).max(10).optional(),
    delay: z.number().min(0).optional(),
    attempts: z.number().min(1).max(10).optional(),
  }).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { options, ...emailData } = requestSchema.parse(body);

    const jobId = await addEmailToQueue(emailData, options);

    log.info('Email added to queue via API', {
      jobId,
      emailType: emailData.type,
      to: emailData.to,
      returnNumber: emailData.returnNumber,
    });

    return NextResponse.json({
      success: true,
      jobId,
      message: '<PERSON><PERSON> added to queue successfully',
    });
  } catch (error) {
    log.error('Failed to add email to queue via API', { error });
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to add email to queue' },
      { status: 500 }
    );
  }
}
