'use server';

import { <PERSON><PERSON><PERSON> } from 'node:buffer';
import { getOrderByName } from '@/app/lib/shopify';
import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

// Get all return requests
export async function getReturnRequests() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  const returnRequests = await database.returnRequest
    .findMany({
      select: {
        id: true,
        orderName: true,
        email: true,
        returnNumber: true,
        returnReason: true,
        exchangeType: true,
        status: true,
        processed: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            returnItems: true,
            defectPhotos: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      where: {
        OR: [{ exchangeType: null }, { exchangeType: 'return' }],
      },
    })
    .then((returnRequests) => serializePrisma(returnRequests));

  return returnRequests;
}

// Get all exchange requests
export async function getExchangeRequests() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  const exchangeRequests = await database.returnRequest
    .findMany({
      select: {
        id: true,
        orderName: true,
        email: true,
        returnNumber: true,
        returnReason: true,
        exchangeType: true,
        status: true,
        processed: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            returnItems: true,
            defectPhotos: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      where: {
        exchangeType: 'exchange',
      },
    })
    .then((exchangeRequests) => serializePrisma(exchangeRequests));

  return exchangeRequests;
}

// Get a specific return request by ID
export async function getReturnRequest(id: string) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  const returnRequest = await database.returnRequest.findUnique({
    where: { id },
    include: {
      returnItems: true,
      defectPhotos: true,
    },
  });

  if (!returnRequest) {
    notFound();
  }

  // find if user is in black list
  const isBlacklisted = await database.blacklistEmail.findUnique({
    where: { email: returnRequest.email },
  });

  let returnCount = 0;
  if (isBlacklisted) {
    returnCount = await database.returnRequest.count({
      where: {
        email: returnRequest.email,
        status: 'pending',
      },
    });
  }

  return {
    returnRequest: serializePrisma(returnRequest),
    isBlacklisted,
    returnCount,
  };
}

// Update a return request
export async function updateReturnRequest(
  id: string,
  data: {
    status?: string;
    processed?: string;
    adminNotes?: string;
    trackingNumber?: string;
  }
) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  // Get the current return request to check for status changes
  const currentRequest = await database.returnRequest.findUnique({
    where: { id },
    select: {
      status: true,
      processed: true,
      email: true,
      returnNumber: true,
      exchangeType: true,
      trackingNumber: true,
    },
  });

  if (!currentRequest) {
    throw new Error('Return request not found');
  }

  const updatedReturnRequest = await database.returnRequest.update({
    where: { id },
    data,
  });

  // Send emails based on status changes
  try {
    const isExchange = currentRequest.exchangeType === 'exchange';

    // Check for status changes that trigger emails
    if (data.status && data.status !== currentRequest.status) {
      if (data.status === 'approved' && currentRequest.status === 'pending') {
        // Manual approval
        const emailType = isExchange
          ? 'exchange-request-manual-approved'
          : 'return-request-manual-approved';

        await queueStatusChangeEmail(emailType, {
          to: currentRequest.email,
          returnNumber: currentRequest.returnNumber,
          ...(isExchange
            ? {}
            : {
                refundDays: '5-7',
                returnInstructions:
                  data.adminNotes ||
                  'Please follow the return instructions provided.',
              }),
        });
      } else if (data.status === 'rejected') {
        // Request declined
        await queueStatusChangeEmail('request-declined', {
          to: currentRequest.email,
          returnNumber: currentRequest.returnNumber,
        });
      }
    }

    // Check for processed status changes that trigger emails
    if (data.processed && data.processed !== currentRequest.processed) {
      if (
        isExchange &&
        data.processed === 'exchange_shipped' &&
        data.trackingNumber
      ) {
        // Exchange item shipped
        await queueStatusChangeEmail('exchange-item-shipped', {
          to: currentRequest.email,
          returnNumber: currentRequest.returnNumber,
          trackingNumber: data.trackingNumber,
        });
      } else if (
        isExchange &&
        data.processed === 'completed' &&
        currentRequest.processed === 'exchange_shipped'
      ) {
        // Exchange completed (return item received)
        await queueStatusChangeEmail('exchange-item-received', {
          to: currentRequest.email,
          returnNumber: currentRequest.returnNumber,
        });
      }
    }
  } catch (emailError) {
    log.error('Error sending status change email:', { error: emailError });
    // Don't fail the update if email fails
  }

  revalidatePath('/return-requests');
  revalidatePath(`/return-requests/${id}`);

  return serializePrisma(updatedReturnRequest);
}

// Helper function to queue status change emails
async function queueStatusChangeEmail(
  emailType: string,
  emailData: {
    to: string;
    returnNumber: string;
    refundDays?: string;
    returnInstructions?: string;
    trackingNumber?: string;
  }
) {
  try {
    // Set priority based on email type
    let priority = 5; // Default priority
    if (emailType.includes('approved')) priority = 8;
    if (emailType.includes('declined')) priority = 9;
    if (emailType.includes('shipped')) priority = 9;

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002'}/api/email-queue/add`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: emailType,
          priority,
          ...emailData,
        }),
      }
    );

    if (response.ok) {
      const result = await response.json();
      log.info(`Queued ${emailType} email for ${emailData.to}`, {
        jobId: result.jobId,
      });
    } else {
      const errorText = await response.text();
      log.error(`Failed to queue ${emailType} email:`, { error: errorText });
    }
  } catch (error) {
    log.error(`Error queueing ${emailType} email:`, { error });
  }
}

// Generate yamato csv
export async function generateYamatoCsv() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  const exchangeRequests = await database.returnRequest.findMany({
    where: {
      exchangeType: 'exchange',
      processed: 'pending',
      trackingNumber: null,
    },
    include: {
      returnItems: true,
    },
  });

  if (exchangeRequests.length === 0) {
    return {
      success: false,
      error: 'No exchange requests found',
    };
  }

  try {
    // const kuroshiro = new Kuroshiro();
    // await kuroshiro.init(new KuromojiAnalyzer());

    const yamatoSetting = {
      customerNo: '090600551570',
      unchinNo: '01',
      kurijouType: 7,
      handling1: 'ワレ物注意',
      handling2: '下積厳禁',
    };
    const fulfillmentSetting = {
      name: 'ケースフィニット株式会社',
      phone: '080 - 6583 - 9620',
      postal: '1700005',
      province: '東京都',
      city: '豊島区南大塚',
      address1: '3-24-4 MTビル 2階',
    };

    // const filepaths: string[] = [];
    const dataCmb: any[] = [];
    const utc0Date = new Date();
    const date = new Date(utc0Date.getTime() + 9 * 60 * 60 * 1000);
    const dateArr = date.toISOString().split('T')[0].split('-');
    const dateStr = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`;

    for (const exchangeRequest of exchangeRequests) {
      const order = await getOrderByName(exchangeRequest.orderName);

      if (!order) {
        throw new Error('Order not found');
      }

      const fulfillableLineItem = exchangeRequest.returnItems;
      const customerName = `${order.shippingAddress?.lastName} ${order.shippingAddress?.firstName}`;
      const phone = order.shippingAddress?.phone ?? '1';
      const prodname1 =
        fulfillableLineItem.length > 0
          ? `${fulfillableLineItem[0]?.title} - ${fulfillableLineItem[0]?.variantTitle}`
          : '';
      const prodname2 =
        fulfillableLineItem.length > 1
          ? `${fulfillableLineItem[1]?.title} - ${fulfillableLineItem[1]?.variantTitle}`
          : '';
      const hankakuAddress =
        `${fulfillmentSetting.province}${fulfillmentSetting.city}${
          fulfillmentSetting.address1 ?? ''
        }`.replace('　', ' ');

      // const data = {
      //   お客様管理番号: exchangeRequest.id,
      //   送り状種別: yamatoSetting.kurijouType,
      //   温度区分: '',
      //   予備4: '',
      //   出荷予定日: `${dateArr[0]}/${dateArr[1]}/${dateArr[2]}`,
      //   配達指定日: '',
      //   配達時間帯区分: '',
      //   届け先コード: '',
      //   届け先電話番号: phone.replace('+81', ''),
      //   '届け先電話番号(枝番)': '',
      //   届け先郵便番号: order.shippingAddress?.zip ?? '',
      //   届け先住所: `${order.shippingAddress?.address1?.replace('　', '')} ${order.shippingAddress?.address2?.replace('　', '')}`,
      //   'お届け先建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': '',
      //   会社・部門名１: order.shippingAddress?.company ?? '',
      //   会社・部門名２: '',
      //   '届け先名(漢字)': customerName,
      //   '届け先名(カナ)': '',
      //   敬称: '',
      //   依頼主コード: '',
      //   依頼主電話番号: `${fulfillmentSetting.phone}`,
      //   '依頼主電話番号(枝番)': '',
      //   依頼主郵便番号: fulfillmentSetting.postal,
      //   依頼主住所: hankakuAddress,
      //   '依頼主建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': '',
      //   '依頼主名（漢字）': fulfillmentSetting.name,
      //   '依頼主名(カナ)': '',
      //   品名コード１:
      //     fulfillableLineItem.length > 0
      //       ? (fulfillableLineItem[0].sku ?? '')
      //       : '',
      //   品名１: prodname1.length <= 20 ? prodname1 : 'スマートフォンケース',
      //   品名コード２:
      //     fulfillableLineItem.length > 1
      //       ? (fulfillableLineItem[1].sku ?? '')
      //       : '',
      //   品名2: prodname2.length <= 20 ? prodname2 : 'スマートフォンケース',
      //   荷扱い１: yamatoSetting.handling1,
      //   荷扱い２: yamatoSetting.handling2,
      //   記事: '',
      //   'コレクト代金引換額(税込)': '',
      //   コレクト内消費税額: '',
      //   営業所止置き: '',
      //   止め置き営業所コード: '',
      //   発行枚数: '',
      //   個数口枠の印字: '',
      //   請求先顧客コード: `${yamatoSetting?.customerNo}`,
      //   請求先分類コード: '',
      //   運賃管理番号: `${yamatoSetting?.unchinNo ?? '01'}`,
      // };

      // Use English keys to avoid encoding issues
      const data = {
        customerManagementNumber: exchangeRequest.id,
        shippingLabelType: yamatoSetting.kurijouType,
        temperatureCategory: '',
        spare4: '',
        plannedShipmentDate: `${dateArr[0]}/${dateArr[1]}/${dateArr[2]}`,
        specifiedDeliveryDate: '',
        deliveryTimeSlot: '',
        destinationCode: '',
        destinationPhoneNumber: phone.replace('+81', ''),
        destinationPhoneNumberExtension: '',
        destinationPostalCode: order.shippingAddress?.zip ?? '',
        destinationAddress: `${order.shippingAddress?.address1?.replace('　', '')} ${order.shippingAddress?.address2?.replace('　', '') ?? ''}`,
        destinationBuildingName: '',
        companyDepartmentName1: order.shippingAddress?.company ?? '',
        companyDepartmentName2: '',
        destinationNameKanji: customerName,
        destinationNameKana: '',
        honorificTitle: '',
        clientCode: '',
        clientPhoneNumber: `${fulfillmentSetting.phone}`,
        clientPhoneNumberExtension: '',
        clientPostalCode: fulfillmentSetting.postal,
        clientAddress: hankakuAddress,
        clientBuildingName: '',
        clientNameKanji: fulfillmentSetting.name,
        clientNameKana: '',
        productCode1:
          fulfillableLineItem.length > 0
            ? (fulfillableLineItem[0].sku ?? '')
            : '',
        productName1:
          prodname1.length <= 20 ? prodname1 : 'スマートフォンケース',
        productCode2:
          fulfillableLineItem.length > 1
            ? (fulfillableLineItem[1].sku ?? '')
            : '',
        productName2:
          prodname2.length <= 20 ? prodname2 : 'スマートフォンケース',
        handling1: yamatoSetting.handling1,
        handling2: yamatoSetting.handling2,
        article: '',
        collectCashOnDeliveryAmount: '',
        collectConsumptionTaxAmount: '',
        branchOfficeStorage: '',
        storageOfficeCode: '',
        issueQuantity: '',
        pieceFramePrint: '',
        billingCustomerCode: `${yamatoSetting?.customerNo}`,
        billingClassificationCode: '',
        freightManagementNumber: `${yamatoSetting?.unchinNo ?? '01'}`,
      };

      dataCmb.push(data);
    }

    const japaneseHeaders = [
      'お客様管理番号',
      '送り状種別',
      '温度区分',
      '予備4',
      '出荷予定日',
      '配達指定日',
      '配達時間帯区分',
      '届け先コード',
      '届け先電話番号',
      '届け先電話番号(枝番)',
      '届け先郵便番号',
      '届け先住所',
      'お届け先建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）',
      '会社・部門名１',
      '会社・部門名２',
      '届け先名(漢字)',
      '届け先名(カナ)',
      '敬称',
      '依頼主コード',
      '依頼主電話番号',
      '依頼主電話番号(枝番)',
      '依頼主郵便番号',
      '依頼主住所',
      '依頼主建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）',
      '依頼主名（漢字）',
      '依頼主名(カナ)',
      '品名コード１',
      '品名１',
      '品名コード２',
      '品名2',
      '荷扱い１',
      '荷扱い２',
      '記事',
      'コレクト代金引換額(税込)',
      'コレクト内消費税額',
      '営業所止置き',
      '止め置き営業所コード',
      '発行枚数',
      '個数口枠の印字',
      '請求先顧客コード',
      '請求先分類コード',
      '運賃管理番号',
    ];

    // Create CSV with custom headers
    let csvContent = `${japaneseHeaders.join(',')}\n`;

    for (const row of dataCmb) {
      const values = Object.values(row).map((value) => {
        // Escape values that contain commas, quotes, or newlines
        const stringValue = String(value || '');
        if (
          stringValue.includes(',') ||
          stringValue.includes('"') ||
          stringValue.includes('\n')
        ) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      });
      csvContent += `${values.join(',')}\n`;
    }

    // Convert to Shift-JIS for Japanese systems
    const encoder = new TextEncoder();
    const csvBuffer = encoder.encode(csvContent);

    return {
      success: true,
      data: Buffer.from(csvBuffer).toString('base64'),
      filename: `yamato-exchange-${dateStr}.csv`,
      mimeType: 'text/csv; charset=utf-8',
    };
  } catch (e) {
    return { error: e };
  }
}

export async function processYamatoCsv(data: { [key: string]: string }[]) {
  try {
    for (const row of data) {
      const trackingNumber = row.伝票番号;
      const returnNumber = row.お客様管理番号;

      // Get current request data before update
      const currentRequest = await database.returnRequest.findUnique({
        where: { id: returnNumber },
        select: {
          email: true,
          returnNumber: true,
          exchangeType: true,
          processed: true,
        },
      });

      await database.returnRequest.update({
        where: { id: returnNumber },
        data: {
          trackingNumber,
          trackingUrl: `https://jizen.kuronekoyamato.co.jp/jizen/servlet/crjz.b.NQ0010?id=${trackingNumber}`,
          trackingCompanyName: 'ヤマト運輸',
          processed: 'exchange_shipped', // Update processed status when tracking is added
        },
      });

      // Send exchange shipped email if this is an exchange
      if (currentRequest?.exchangeType === 'exchange') {
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002'}/api/email-queue/add`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                type: 'exchange-item-shipped',
                to: currentRequest.email,
                returnNumber: currentRequest.returnNumber,
                trackingNumber,
                priority: 9, // Very high priority for shipping notifications
              }),
            }
          );

          if (response.ok) {
            const result = await response.json();
            log.info(
              `Queued exchange-item-shipped email for ${currentRequest.email}`,
              { jobId: result.jobId }
            );
          } else {
            const errorText = await response.text();
            log.error('Failed to queue exchange shipped email:', {
              error: errorText,
            });
          }
        } catch (emailError) {
          log.error('Error queueing exchange shipped email:', {
            error: emailError,
          });
        }
      }
    }

    return { success: true };
  } catch (e) {
    return { error: e };
  }
}
