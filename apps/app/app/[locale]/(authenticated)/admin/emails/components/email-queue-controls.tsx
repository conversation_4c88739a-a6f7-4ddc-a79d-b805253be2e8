'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { useToast } from '@repo/design-system/components/ui/use-toast';
import { Pause, Play, Trash2 } from 'lucide-react';
import { useState } from 'react';

export function EmailQueueControls() {
  const [loading, setLoading] = useState<string | null>(null);
  const { toast } = useToast();

  const handleAction = async (action: string, jobId?: string) => {
    setLoading(action);
    try {
      const response = await fetch('/api/email-queue/manage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, jobId }),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: result.message,
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="flex flex-wrap gap-4">
      <Button
        onClick={() => handleAction('pause')}
        disabled={loading === 'pause'}
        variant="outline"
        size="sm"
      >
        <Pause className="mr-2 h-4 w-4" />
        {loading === 'pause' ? 'Pausing...' : 'Pause Queue'}
      </Button>

      <Button
        onClick={() => handleAction('resume')}
        disabled={loading === 'resume'}
        variant="outline"
        size="sm"
      >
        <Play className="mr-2 h-4 w-4" />
        {loading === 'resume' ? 'Resuming...' : 'Resume Queue'}
      </Button>

      <Button
        onClick={() => handleAction('clean')}
        disabled={loading === 'clean'}
        variant="outline"
        size="sm"
      >
        <Trash2 className="mr-2 h-4 w-4" />
        {loading === 'clean' ? 'Cleaning...' : 'Clean Old Jobs'}
      </Button>
    </div>
  );
}
