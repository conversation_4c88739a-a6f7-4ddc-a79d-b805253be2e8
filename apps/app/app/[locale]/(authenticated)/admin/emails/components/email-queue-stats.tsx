'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Activity, CheckCircle, Clock, Loader, XCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  total: number;
  redisHealthy: boolean;
  timestamp: string;
}

export function EmailQueueStats() {
  const [stats, setStats] = useState<QueueStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // const { data: stats, error, isLoading: loading } = useSWR('/api/email-queue/stats');

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/email-queue/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch queue stats');
      }
      const result = await response.json();
      setStats(result.data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    fetchStats();
    const interval = setInterval(fetchStats, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return <div>Loading queue statistics...</div>;
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardContent>
          <CardDescription className="text-destructive">
            {/* {error?.message?.toString() ?? 'Failed to fetch queue stats'} */}
            {error?.toString()}
          </CardDescription>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return null;
  }

  const statCards = [
    {
      title: 'Waiting',
      value: stats.waiting,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Active',
      value: stats.active,
      icon: Loader,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Completed',
      value: stats.completed,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Failed',
      value: stats.failed,
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  return (
    <div className="space-y-4">
      {/* Redis Health Status */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">System Health</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Badge variant={stats.redisHealthy ? 'default' : 'destructive'}>
              Redis: {stats.redisHealthy ? 'Healthy' : 'Unhealthy'}
            </Badge>
            <span className="text-muted-foreground text-xs">
              Last updated: {new Date(stats.timestamp).toLocaleTimeString()}
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Queue Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="font-medium text-sm">
                  {stat.title}
                </CardTitle>
                <div className={`rounded-full p-2 ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="font-bold text-2xl">{stat.value}</div>
                <p className="text-muted-foreground text-xs">
                  {stat.title.toLowerCase()} jobs
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Total Jobs */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Total Jobs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">{stats.total}</div>
          <p className="text-muted-foreground text-xs">All jobs in the queue</p>
        </CardContent>
      </Card>
    </div>
  );
}
