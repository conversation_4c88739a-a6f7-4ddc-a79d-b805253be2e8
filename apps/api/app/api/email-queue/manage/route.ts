import { NextRequest, NextResponse } from 'next/server';
import { pauseQueue, resumeQueue, cleanQueue, retryJob, getJob } from '@repo/email-queue';
import { log } from '@repo/observability/log';
import { z } from 'zod';

const actionSchema = z.object({
  action: z.enum(['pause', 'resume', 'clean', 'retry']),
  jobId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, jobId } = actionSchema.parse(body);

    let result;
    switch (action) {
      case 'pause':
        await pauseQueue();
        result = { message: 'Queue paused successfully' };
        break;
      
      case 'resume':
        await resumeQueue();
        result = { message: 'Queue resumed successfully' };
        break;
      
      case 'clean':
        await cleanQueue();
        result = { message: 'Queue cleaned successfully' };
        break;
      
      case 'retry':
        if (!jobId) {
          return NextResponse.json(
            { success: false, error: 'Job ID is required for retry action' },
            { status: 400 }
          );
        }
        await retryJob(jobId);
        result = { message: `Job ${jobId} retried successfully` };
        break;
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    log.info('Queue management action completed', { action, jobId });

    return NextResponse.json({
      success: true,
      ...result,
    });
  } catch (error) {
    log.error('Queue management action failed', { error });
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Queue management action failed' },
      { status: 500 }
    );
  }
}

// Get job details
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      );
    }

    const job = await getJob(jobId);
    
    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: job.id,
        name: job.name,
        data: job.data,
        opts: job.opts,
        progress: job.progress,
        attemptsMade: job.attemptsMade,
        finishedOn: job.finishedOn,
        processedOn: job.processedOn,
        timestamp: job.timestamp,
        returnvalue: job.returnvalue,
        failedReason: job.failedReason,
      },
    });
  } catch (error) {
    log.error('Failed to get job details', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to get job details' },
      { status: 500 }
    );
  }
}
