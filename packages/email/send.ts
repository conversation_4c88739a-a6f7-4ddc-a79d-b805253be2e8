import { render } from '@react-email/components';
import { sendgrid } from './index';
import { keys } from './keys';
import type { ReactElement } from 'react';

const env = keys();

export interface SendEmailOptions {
  to: string | string[];
  subject: string;
  template: ReactElement;
  from?: string;
}

export async function sendEmail({
  to,
  subject,
  template,
  from = env.SENDGRID_FROM,
}: SendEmailOptions) {
  try {
    const html = await render(template);
    
    const msg = {
      to: Array.isArray(to) ? to : [to],
      from,
      subject,
      html,
    };

    const result = await sendgrid.send(msg);
    return { success: true, result };
  } catch (error) {
    console.error('Failed to send email:', error);
    return { success: false, error };
  }
}

// Specific email sending functions for each template type
export async function sendReturnRequestAutoApprovedEmail(
  to: string,
  returnNumber: string,
  refundDays?: string
) {
  const { ReturnRequestAutoApprovedEmail } = await import('./templates/return-request-auto-approved');
  
  return sendEmail({
    to,
    subject: `Return Request #${returnNumber} Approved`,
    template: ReturnRequestAutoApprovedEmail({ returnNumber, refundDays }),
  });
}

export async function sendReturnRequestManualReceivedEmail(
  to: string,
  returnNumber: string,
  reviewDays?: string
) {
  const { ReturnRequestManualReceivedEmail } = await import('./templates/return-request-manual-received');
  
  return sendEmail({
    to,
    subject: `Return Request #${returnNumber} Received`,
    template: ReturnRequestManualReceivedEmail({ returnNumber, reviewDays }),
  });
}

export async function sendReturnRequestManualApprovedEmail(
  to: string,
  returnNumber: string,
  refundDays?: string,
  returnInstructions?: string
) {
  const { ReturnRequestManualApprovedEmail } = await import('./templates/return-request-manual-approved');
  
  return sendEmail({
    to,
    subject: `Return Request #${returnNumber} Approved`,
    template: ReturnRequestManualApprovedEmail({ returnNumber, refundDays, returnInstructions }),
  });
}

export async function sendExchangeRequestAutoApprovedEmail(
  to: string,
  returnNumber: string
) {
  const { ExchangeRequestAutoApprovedEmail } = await import('./templates/exchange-request-auto-approved');
  
  return sendEmail({
    to,
    subject: `Exchange Request #${returnNumber} Approved`,
    template: ExchangeRequestAutoApprovedEmail({ returnNumber }),
  });
}

export async function sendExchangeItemShippedEmail(
  to: string,
  returnNumber: string,
  trackingNumber: string
) {
  const { ExchangeItemShippedEmail } = await import('./templates/exchange-item-shipped');
  
  return sendEmail({
    to,
    subject: `Replacement Item Shipped - Request #${returnNumber}`,
    template: ExchangeItemShippedEmail({ returnNumber, trackingNumber }),
  });
}

export async function sendExchangeItemReceivedEmail(
  to: string,
  returnNumber: string
) {
  const { ExchangeItemReceivedEmail } = await import('./templates/exchange-item-received');
  
  return sendEmail({
    to,
    subject: `Exchange Complete - Request #${returnNumber}`,
    template: ExchangeItemReceivedEmail({ returnNumber }),
  });
}

export async function sendExchangeRequestManualReceivedEmail(
  to: string,
  returnNumber: string
) {
  const { ExchangeRequestManualReceivedEmail } = await import('./templates/exchange-request-manual-received');
  
  return sendEmail({
    to,
    subject: `Exchange Request #${returnNumber} Received`,
    template: ExchangeRequestManualReceivedEmail({ returnNumber }),
  });
}

export async function sendExchangeRequestManualApprovedEmail(
  to: string,
  returnNumber: string
) {
  const { ExchangeRequestManualApprovedEmail } = await import('./templates/exchange-request-manual-approved');
  
  return sendEmail({
    to,
    subject: `Exchange Request #${returnNumber} Approved`,
    template: ExchangeRequestManualApprovedEmail({ returnNumber }),
  });
}

export async function sendRequestDeclinedEmail(
  to: string,
  returnNumber: string
) {
  const { RequestDeclinedEmail } = await import('./templates/request-declined');
  
  return sendEmail({
    to,
    subject: `Request #${returnNumber} Declined`,
    template: RequestDeclinedEmail({ returnNumber }),
  });
}
