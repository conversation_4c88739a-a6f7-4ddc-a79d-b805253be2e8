# Email Package

This package provides email templates and sending functionality for the returns system using SendGrid.

## Setup

### Environment Variables

Add the following environment variables to your `.env` file:

```env
# SendGrid Configuration
SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
SENDGRID_FROM=<EMAIL>

# Resend Configuration (legacy)
RESEND_TOKEN=re_your_resend_token_here
RESEND_FROM=<EMAIL>
```

## Email Templates

The following email templates are available:

1. **Return Request Auto Approved** - `ReturnRequestAutoApprovedEmail`
2. **Return Request Manual Received** - `ReturnRequestManualReceivedEmail`
3. **Return Request Manual Approved** - `ReturnRequestManualApprovedEmail`
4. **Exchange Request Auto Approved** - `ExchangeRequestAutoApprovedEmail`
5. **Exchange Item Shipped** - `ExchangeItemShippedEmail`
6. **Exchange Item Received** - `ExchangeItemReceivedEmail`
7. **Exchange Request Manual Received** - `ExchangeRequestManualReceivedEmail`
8. **Exchange Request Manual Approved** - `ExchangeRequestManualApprovedEmail`
9. **Request Declined** - `RequestDeclinedEmail`

## Usage

### Direct Function Calls

```typescript
import { sendReturnRequestAutoApprovedEmail } from '@repo/email';

// Send return request auto approved email
const result = await sendReturnRequestAutoApprovedEmail(
  '<EMAIL>',
  'RET-12345',
  '5-7' // optional refund days
);

if (result.success) {
  console.log('Email sent successfully');
} else {
  console.error('Failed to send email:', result.error);
}
```

### API Routes

The API provides endpoints for sending emails:

#### Individual Email Endpoints

- `POST /api/email/return-request-auto-approved`
- `POST /api/email/return-request-manual-received`
- `POST /api/email/return-request-manual-approved`
- `POST /api/email/exchange-request-auto-approved`
- `POST /api/email/exchange-item-shipped`
- `POST /api/email/exchange-item-received`
- `POST /api/email/exchange-request-manual-received`
- `POST /api/email/exchange-request-manual-approved`
- `POST /api/email/request-declined`

#### Universal Email Endpoint

`POST /api/email/send` - Handles all email types based on the `type` field.

### API Request Examples

#### Return Request Auto Approved

```json
{
  "to": "<EMAIL>",
  "returnNumber": "RET-12345",
  "refundDays": "5-7"
}
```

#### Exchange Item Shipped

```json
{
  "to": "<EMAIL>",
  "returnNumber": "EXC-12345",
  "trackingNumber": "1Z999AA1234567890"
}
```

#### Universal Send Endpoint

```json
{
  "type": "return-request-auto-approved",
  "to": "<EMAIL>",
  "returnNumber": "RET-12345",
  "refundDays": "5-7"
}
```

## Email Template Props

### ReturnRequestAutoApprovedEmail
- `returnNumber: string` - The return request number
- `refundDays?: string` - Number of business days for refund (default: "5-7")

### ReturnRequestManualReceivedEmail
- `returnNumber: string` - The return request number
- `reviewDays?: string` - Number of business days for review (default: "3")

### ReturnRequestManualApprovedEmail
- `returnNumber: string` - The return request number
- `refundDays?: string` - Number of business days for refund (default: "5-7")
- `returnInstructions?: string` - Additional return instructions

### ExchangeItemShippedEmail
- `returnNumber: string` - The exchange request number
- `trackingNumber: string` - The tracking number for the shipped item

### All Other Templates
- `returnNumber: string` - The request number

## Development

To preview email templates, you can use the example components exported from each template file:

```typescript
import ExampleReturnRequestAutoApprovedEmail from '@repo/email/templates/return-request-auto-approved';

// Use in your development environment to preview the email
```
