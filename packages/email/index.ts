import sgMail from '@sendgrid/mail';
import { Resend } from 'resend';
import { keys } from './keys';

const env = keys();

export const resend = new Resend(env.RESEND_TOKEN);

// Initialize SendGrid
sgMail.setApiKey(env.SENDGRID_API_KEY);
export const sendgrid = sgMail;

// Export email templates
export { ContactTemplate } from './templates/contact';
export { ReturnRequestAutoApprovedEmail } from './templates/return-request-auto-approved';
export { ReturnRequestManualReceivedEmail } from './templates/return-request-manual-received';
export { ReturnRequestManualApprovedEmail } from './templates/return-request-manual-approved';
export { ExchangeRequestAutoApprovedEmail } from './templates/exchange-request-auto-approved';
export { ExchangeItemShippedEmail } from './templates/exchange-item-shipped';
export { ExchangeItemReceivedEmail } from './templates/exchange-item-received';
export { ExchangeRequestManualReceivedEmail } from './templates/exchange-request-manual-received';
export { ExchangeRequestManualApprovedEmail } from './templates/exchange-request-manual-approved';
export { RequestDeclinedEmail } from './templates/request-declined';

// Export email sending functions
export * from './send';
