{"name": "@repo/email", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@react-email/components": "0.0.36", "@sendgrid/mail": "^8.1.5", "@t3-oss/env-nextjs": "^0.12.0", "react": "^19.1.0", "react-dom": "^19.1.0", "resend": "^4.3.0", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "^19.1.2", "typescript": "^5.8.3"}}