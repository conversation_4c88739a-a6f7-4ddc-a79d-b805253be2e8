import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

type ReturnRequestManualReceivedEmailProps = {
  readonly returnNumber: string;
  readonly reviewDays?: string;
};

export const ReturnRequestManualReceivedEmail = ({
  returnNumber,
  reviewDays = '3',
}: ReturnRequestManualReceivedEmailProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>Your return request #{returnNumber} is being reviewed</Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Img
            className="mx-auto"
            src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
          />
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                Return Request Received
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                Your return request #{returnNumber} is being reviewed.
              </Text>
              <Hr className="my-4" />
              <Text className="m-0 text-zinc-700">
                Our customer service team will verify your request and respond
                within {reviewDays} business days.
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleReturnRequestManualReceivedEmail = () => (
  <ReturnRequestManualReceivedEmail returnNumber="RET-12345" reviewDays="3" />
);

export default ExampleReturnRequestManualReceivedEmail;
