import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

type ExchangeItemShippedEmailProps = {
  readonly returnNumber: string;
  readonly trackingNumber: string;
};

export const ExchangeItemShippedEmail = ({
  returnNumber,
  trackingNumber,
}: ExchangeItemShippedEmailProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>
        Your replacement item for request #{returnNumber} has shipped
      </Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Img
            className="mx-auto"
            src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
          />
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                Replacement Item Shipped
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                Your replacement item for request #{returnNumber} has shipped.
              </Text>
              <Hr className="my-4" />
              <Text className="m-0 mb-4 text-zinc-700">
                <strong>Tracking:</strong> {trackingNumber}
              </Text>
              <Text className="m-0 text-zinc-700">
                Please use the enclosed prepaid label to return your original
                item.
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleExchangeItemShippedEmail = () => (
  <ExchangeItemShippedEmail
    returnNumber="EXC-12345"
    trackingNumber="1Z999AA1234567890"
  />
);

export default ExampleExchangeItemShippedEmail;
