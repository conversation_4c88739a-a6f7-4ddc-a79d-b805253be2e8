import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

type ReturnRequestManualApprovedEmailProps = {
  readonly returnNumber: string;
  readonly refundDays?: string;
  readonly returnInstructions?: string;
};

export const ReturnRequestManualApprovedEmail = ({
  returnNumber,
  refundDays = '5-7',
  returnInstructions,
}: ReturnRequestManualApprovedEmailProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>Your return request #{returnNumber} has been approved</Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Img
            className="mx-auto"
            src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
          />
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                Return Request Approved
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                Your return request #{returnNumber} has been approved.
              </Text>
              <Hr className="my-4" />
              <Text className="m-0 mb-4 text-zinc-700">
                <strong>Next steps:</strong> Please follow the return shipping
                instructions or use the prepaid label provided.
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                <strong>Refund:</strong> You'll receive your refund within{' '}
                {refundDays} business days once we receive your item.
              </Text>
              {returnInstructions && (
                <>
                  <Hr className="my-4" />
                  <Text className="m-0 text-zinc-700">
                    <strong>Return Instructions:</strong>
                  </Text>
                  <Text className="m-0 text-zinc-700">
                    {returnInstructions}
                  </Text>
                </>
              )}
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleReturnRequestManualApprovedEmail = () => (
  <ReturnRequestManualApprovedEmail
    returnNumber="RET-12345"
    refundDays="5-7"
    returnInstructions="Please package the item securely and use the provided prepaid shipping label."
  />
);

export default ExampleReturnRequestManualApprovedEmail;
