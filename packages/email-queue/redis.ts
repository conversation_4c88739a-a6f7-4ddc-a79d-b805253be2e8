import { Redis } from '@upstash/redis';
import IORedis from 'ioredis';
import { keys } from './keys';

const env = keys();

// Upstash Redis client for REST API
export const upstashRedis = new Redis({
  url: env.UPSTASH_REDIS_REST_URL,
  token: env.UPSTASH_REDIS_REST_TOKEN,
});

// IORedis client for BullMQ (requires direct Redis connection)
// If REDIS_URL is provided, use it; otherwise, create a connection using Upstash credentials
export const createRedisConnection = (): IORedis => {
  if (env.REDIS_URL) {
    return new IORedis(env.REDIS_URL, {
      maxRetriesPerRequest: 3,
      // retryDelayOnFailover: 100,
      enableReadyCheck: false,
      lazyConnect: true,
    });
  }

  // For Upstash, we need to extract connection details from the REST URL
  // This is a fallback - ideally you should use REDIS_URL for BullMQ
  const url = new URL(env.UPSTASH_REDIS_REST_URL);
  const host = url.hostname.replace('-rest', ''); // Remove -rest from hostname
  const port = 6379; // Standard Redis port

  return new IORedis({
    host,
    port,
    password: env.UPSTASH_REDIS_REST_TOKEN,
    tls: {
      rejectUnauthorized: false,
    },
    maxRetriesPerRequest: 3,
    // retryDelayOnFailover: 100,
    enableReadyCheck: false,
    lazyConnect: true,
  });
};

// Singleton Redis connection for BullMQ
let redisConnection: IORedis | null = null;

export const getRedisConnection = (): IORedis => {
  if (!redisConnection) {
    redisConnection = createRedisConnection();
  }
  return redisConnection;
};

// Health check function
export const checkRedisHealth = async (): Promise<boolean> => {
  try {
    const redis = getRedisConnection();
    await redis.ping();
    return true;
  } catch (error) {
    console.error('Redis health check failed:', error);
    return false;
  }
};
