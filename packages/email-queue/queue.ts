import { log } from '@repo/observability/log';
import { Queue, QueueEvents } from 'bullmq';
import { getRedisConnection } from './redis';
import type { EmailJobData, EmailJobOptions } from './types';

// Queue configuration
const QUEUE_NAME = 'email-queue';
const DEFAULT_JOB_OPTIONS: EmailJobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 2000,
  },
  removeOnComplete: 100, // Keep last 100 completed jobs
  removeOnFail: 50, // Keep last 50 failed jobs
};

// Create the email queue
export const emailQueue = new Queue<EmailJobData>(QUEUE_NAME, {
  connection: getRedisConnection(),
  defaultJobOptions: DEFAULT_JOB_OPTIONS,
});

// Queue events for monitoring
export const emailQueueEvents = new QueueEvents(QUEUE_NAME, {
  connection: getRedisConnection(),
});

// Event listeners for logging and monitoring
emailQueueEvents.on('completed', ({ jobId, returnvalue }) => {
  log.info('Email job completed', { jobId, result: returnvalue });
});

emailQueueEvents.on('failed', ({ jobId, failedReason }) => {
  log.error('Email job failed', { jobId, error: failedReason });
});

// Add email to queue
export const addEmailToQueue = async (
  emailData: EmailJobData,
  options?: EmailJobOptions
): Promise<string> => {
  try {
    const job = await emailQueue.add('send-email', emailData, {
      ...DEFAULT_JOB_OPTIONS,
      ...options,
      priority: emailData.priority || 5,
      delay: emailData.delay || 0,
      attempts: emailData.attempts || 3,
    });

    log.info('Email job added to queue', {
      jobId: job.id,
      emailType: emailData.type,
      to: emailData.to,
      returnNumber: emailData.returnNumber,
    });

    if (!job.id) {
      throw new Error('Failed to add email job to queue');
    }

    return job.id;
  } catch (error) {
    log.error('Failed to add email job to queue', { error, emailData });
    throw error;
  }
};

// Get queue statistics
export const getQueueStats = async () => {
  try {
    const [waiting, active, completed, failed] = await Promise.all([
      emailQueue.getWaiting(),
      emailQueue.getActive(),
      emailQueue.getCompleted(),
      emailQueue.getFailed(),
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length,
    };
  } catch (error) {
    log.error('Failed to get queue stats', { error });
    throw error;
  }
};

// Clean up old jobs
export const cleanQueue = async () => {
  try {
    await emailQueue.clean(24 * 60 * 60 * 1000, 100, 'completed'); // Clean completed jobs older than 24h
    await emailQueue.clean(7 * 24 * 60 * 60 * 1000, 50, 'failed'); // Clean failed jobs older than 7 days
    log.info('Queue cleaned successfully');
  } catch (error) {
    log.error('Failed to clean queue', { error });
  }
};

// Pause/Resume queue
export const pauseQueue = async (): Promise<void> => {
  await emailQueue.pause();
  log.info('Email queue paused');
};

export const resumeQueue = async (): Promise<void> => {
  await emailQueue.resume();
  log.info('Email queue resumed');
};

// Get job by ID
export const getJob = (jobId: string) => {
  return emailQueue.getJob(jobId);
};

// Retry failed job
export const retryJob = async (jobId: string) => {
  const job = await emailQueue.getJob(jobId);
  if (job) {
    await job.retry();
    log.info('Job retried', { jobId });
  }
};
