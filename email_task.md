Generate react-email components for the following email templates.

For each template, create a separate React functional component (e.g., `ReturnRequestAutoApprovedEmail`).
Each component should:
- Import necessary components from 'react-email' (e.g., `Html`, `Head`, `Body`, `Container`, `Text`, `Link`).
- Accept props for dynamic content (e.g., `returnNumber`, `trackingNumber`).
- Use the `Html`, `Head`, and `Body` wrapper elements.
- Use `Container` for layout and `Text` for paragraphs.
- For links, use the `Link` component.
- Ensure all content from the provided Markdown is accurately translated into the respective React component, replacing `{{PLACEHOLDER}}` with props.
- Add basic inline styling for readability (e.g., `fontFamily`, `fontSize`, `lineHeight`, `color`).
- Export each component as a default.

Here are the email templates in Markdown format:

# Email Templates

## Return Request Auto Approved
Your return request #{{Return Number}} has been approved.
Next steps: [Include return shipping instructions or prepaid label info]
Refund: You'll receive your refund within 5-7 business days once we receive your item.

## Return Request Manual Approved
1. Upon return request received
Your return request #{{Return Number}} is being reviewed.
Our customer service team will verify your request and respond within 3 business days.

2. Upon approval
Your return request #{{Return Number}} has been approved.
Next steps: [Include return shipping instructions or prepaid label info]
Refund: You'll receive your refund within 5-7 business days once we receive your item.
[Include return instructions]

## Exchange Request Auto Approved
1. Upoon exchange request received
Your exchange request #{{Return Number}} has been approved.

2. Upon exchange item shipped
Your replacement item for request #{{Return Number}} has shipped.
Tracking: {{Tracking Number}}
Please use the enclosed prepaid label to return your original item.

3. Upon return item received
We've received your returned item for exchange #{{Return Number}}.
Your exchange is now complete. Thank you!

## Exchange Request Manual Approved
1. Upon exchange request received
Your exchange request #{{Return Number}} is being reviewed.

2. Upon approval
Your exchange request #{{Return Number}} has been approved.

3. Upon exchange item shipped
Your replacement item for request #{{Return Number}} has shipped.
Tracking: {{Tracking Number}}
Please use the enclosed prepaid label to return your original item.

3. Upon return item received
We've received your returned item for exchange #{{Return Number}}.
Your exchange is now complete. Thank you!

## Any requests declined:
Your request #{{Return Number}} has been declined

Your request has been declined, Please note that we cannot accept returns or exchanges in the following cases:

- Returns due to customer reasons (size doesn't fit, different from expectations, wrong order, etc.)
- Products that have been more than 7 days since arrival, or products that have been used once (please return in unopened condition)
- Products that have become soiled, scratched, or damaged while in the customer's possession (including damage or scratches from dropping)
- Items missing tags, accessories, boxes, or other accompanying items
Cases where we cannot confirm the customer's purchase

Sorry